/* tslint:disable */
/* eslint-disable */

/* auto-generated by NAPI-RS */

export interface JsEntry {
  slot: number
  numHashes: number
  hash: Buffer
  transactions: Array<JsTransaction>
}
export interface JsTransaction {
  signatures: Array<Buffer>
  message: JsVersionedMessage
}
export interface JsVersionedMessage {
  version: number
  header: JsMessageHeader
  accountKeys: Array<Buffer>
  recentBlockhash: Buffer
  instructions: Array<JsCompiledInstruction>
  addressTableLookups?: Array<JsAddressTableLookup>
}
export interface JsMessageHeader {
  numRequiredSignatures: number
  numReadonlySignedAccounts: number
  numReadonlyUnsignedAccounts: number
}
export interface JsCompiledInstruction {
  programIdIndex: number
  accounts: Array<number>
  data: Buffer
}
export interface JsAddressTableLookup {
  accountKey: Buffer
  writableIndexes: Array<number>
  readonlyIndexes: Array<number>
}
export declare function decodeEntries(slot: number, data: Buffer): Array<JsEntry>
export declare function sum(a: number, b: number): number
