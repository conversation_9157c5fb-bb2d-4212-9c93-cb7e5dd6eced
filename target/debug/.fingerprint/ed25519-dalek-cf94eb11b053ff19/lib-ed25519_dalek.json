{"rustc": 15497389221046826682, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 5347358027863023418, "path": 17749628708327961150, "deps": [[2932480923465029663, "zeroize", false, 17995901767091142760], [4731167174326621189, "rand", false, 851537482489755799], [9431183304631869056, "curve25519_dalek", false, 12624115365221585926], [9689903380558560274, "serde_crate", false, 12219131855167445381], [11472355562936271783, "sha2", false, 16643723428185490067], [16629266738323756185, "ed25519", false, 5997427191514845231]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-cf94eb11b053ff19/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}