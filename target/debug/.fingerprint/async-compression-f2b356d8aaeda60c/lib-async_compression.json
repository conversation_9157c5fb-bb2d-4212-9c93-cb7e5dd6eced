{"rustc": 15497389221046826682, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 5347358027863023418, "path": 8327115925245971152, "deps": [[1906322745568073236, "pin_project_lite", false, 10604144968048648353], [3129130049864710036, "memchr", false, 16410322906155543422], [7620660491849607393, "futures_core", false, 2045025667838836927], [9538054652646069845, "tokio", false, 5286386753884240361], [9556762810601084293, "brotli", false, 14807405258106976631], [10563170702865159712, "flate2", false, 8923911613459592341]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-f2b356d8aaeda60c/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}