{"rustc": 15497389221046826682, "features": "[\"proc-macro-crate\", \"std\"]", "declared_features": "[\"complex-expressions\", \"default\", \"external_doc\", \"proc-macro-crate\", \"std\"]", "target": 600225532800476200, "profile": 3033921117576893, "path": 8435976826356123234, "deps": [[3060637413840920116, "proc_macro2", false, 9689789723025982531], [15203748914246919255, "proc_macro_crate", false, 17791205356841615146], [17990358020177143287, "quote", false, 16463695800006207511], [18149961000318489080, "syn", false, 13915175727837682070]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/num_enum_derive-e23d7ff848b1134d/dep-lib-num_enum_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}