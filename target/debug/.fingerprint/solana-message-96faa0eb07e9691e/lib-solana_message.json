{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"serde\"]", "target": 7288017095310012413, "profile": 3503294605475723963, "path": 18210134523649417835, "deps": [[8611296141060937248, "solana_hash", false, 13757526757637227314], [9556858120010252096, "solana_transaction_error", false, 15058459621352422802], [10570260326288551891, "solana_instruction", false, 1372316627393696122], [11091540729177102731, "solana_pubkey", false, 1204822728850583006], [14591356476411885690, "solana_sdk_ids", false, 6221461119085832789], [15429715045911386410, "solana_sanitize", false, 3642558935210287225], [17917672826516349275, "lazy_static", false, 5846765278004992335]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-message-96faa0eb07e9691e/dep-lib-solana_message", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}