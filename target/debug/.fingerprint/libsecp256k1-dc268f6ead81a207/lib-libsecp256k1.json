{"rustc": 15497389221046826682, "features": "[\"hmac\", \"hmac-drbg\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "declared_features": "[\"default\", \"hmac\", \"hmac-drbg\", \"lazy-static-context\", \"lazy_static\", \"sha2\", \"static-context\", \"std\", \"typenum\"]", "target": 3229137391415082075, "profile": 5347358027863023418, "path": 9818208591921011384, "deps": [[326483822194815791, "hmac_drbg", false, 1899882821956989799], [4731167174326621189, "rand", false, 851537482489755799], [6374421995994392543, "digest", false, 15833774880863655423], [9529943735784919782, "arrayref", false, 2051602663098756662], [9689903380558560274, "serde", false, 12219131855167445381], [10697153736615144157, "build_script_build", false, 3003624078555705630], [11472355562936271783, "sha2", false, 16643723428185490067], [13443824959912985638, "libsecp256k1_core", false, 4389970740085148884], [17001665395952474378, "typenum", false, 13706820078489218761], [17072468807347166763, "base64", false, 3003162179577688622]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsecp256k1-dc268f6ead81a207/dep-lib-libsecp256k1", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}