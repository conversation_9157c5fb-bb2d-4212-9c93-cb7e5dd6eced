{"rustc": 15497389221046826682, "features": "[\"alloc\", \"futures-io\", \"io\", \"memchr\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 17099334823361099506, "deps": [[5103565458935487, "futures_io", false, 2892278637619929771], [1615478164327904835, "pin_utils", false, 4559012445482954715], [1906322745568073236, "pin_project_lite", false, 10604144968048648353], [3129130049864710036, "memchr", false, 16410322906155543422], [6955678925937229351, "slab", false, 10961652678365741924], [7620660491849607393, "futures_core", false, 2045025667838836927], [16240732885093539806, "futures_task", false, 1287680768380148243]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-cc8dc82d82dd33c9/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}