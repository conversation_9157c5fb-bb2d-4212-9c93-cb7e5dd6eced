{"rustc": 15497389221046826682, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 2908210774301854779, "path": 3707578795294071107, "deps": [[15407850927583745935, "adler2", false, 12513260180534934875]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-985879680f7f5207/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}