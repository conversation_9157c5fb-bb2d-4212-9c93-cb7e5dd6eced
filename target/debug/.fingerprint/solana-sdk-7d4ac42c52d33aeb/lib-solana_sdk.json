{"rustc": 15497389221046826682, "features": "[\"borsh\", \"byteorder\", \"chrono\", \"default\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\"]", "declared_features": "[\"borsh\", \"byteorder\", \"chrono\", \"curve25519-dalek\", \"default\", \"dev-context-only-utils\", \"digest\", \"ed25519-dalek\", \"ed25519-dalek-bip32\", \"frozen-abi\", \"full\", \"generic-array\", \"libsecp256k1\", \"memmap2\", \"program\", \"qualifier_attr\", \"rand\", \"rand0-7\", \"serde_json\", \"sha3\"]", "target": 18327772260806902435, "profile": 5347358027863023418, "path": 4786786909067926345, "deps": [[65234016722529558, "bincode", false, 12063455945536758692], [757899038044743028, "serde_with", false, 6452382197150505935], [1230783206204459120, "urip<PERSON>e", false, 14465891807367203231], [1470679118034951355, "num_enum", false, 8970229020012353532], [3712811570531045576, "byteorder", false, 15945256269500855419], [4258399515347749257, "pbkdf2", false, 14498816839948902651], [4731167174326621189, "rand0_7", false, 851537482489755799], [5092398082731730447, "derivation_path", false, 4094843299420215093], [5986029879202738730, "log", false, 3889190429195040124], [6157328561513292295, "build_script_build", false, 13279029672461151995], [6203123018298125816, "borsh", false, 15187726643920293170], [6616501577376279788, "bs58", false, 960472339772559361], [7858942147296547339, "rustversion", false, 2277225609138854432], [7896293946984509699, "bitflags", false, 12356111510501030882], [8008191657135824715, "thiserror", false, 11792957756206362393], [8079500665534101559, "siphasher", false, 1654080713978183967], [8392100871557128840, "solana_sdk_macro", false, 14051279613509081368], [9209347893430674936, "hmac", false, 5426113681420594324], [9689903380558560274, "serde", false, 12219131855167445381], [9857275760291862238, "sha2", false, 7164177083359701474], [9897246384292347999, "chrono", false, 14306334177480499799], [10504454274054532777, "memmap2", false, 8590975871878786060], [10520923840501062997, "generic_array", false, 13142611328727999623], [10697153736615144157, "libsecp256k1", false, 15046899076411068989], [10889494155287625682, "serde_bytes", false, 8894386435297779342], [11017232866922121725, "sha3", false, 3767666912770279253], [13024038960712206194, "qstring", false, 6745259543679877759], [13208667028893622512, "rand", false, 12010585084947804039], [14074610438553418890, "bytemuck", false, 17952071257271532201], [14931062873021150766, "itertools", false, 12459882848868528484], [15246557919602675095, "bytemuck_derive", false, 9878702009835965004], [15367738274754116744, "serde_json", false, 2115698593688149474], [15407337108592562583, "ed25519_dalek_bip32", false, 8101228333052454144], [16257276029081467297, "serde_derive", false, 2401885530386616965], [17231035582851916918, "solana_program", false, 5702810878037597241], [17475753849556516473, "digest", false, 12188470112320313492], [17917672826516349275, "lazy_static", false, 5846765278004992335], [17987314850127689447, "ed25519_dalek", false, 14645881340720048442]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-sdk-7d4ac42c52d33aeb/dep-lib-solana_sdk", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}