{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"bincode\", \"blake3\", \"dev-context-only-utils\", \"frozen-abi\", \"precompiles\", \"serde\", \"verify\"]", "target": 16224605487295323630, "profile": 5347358027863023418, "path": 499240263699013426, "deps": [[8611296141060937248, "solana_hash", false, 13757526757637227314], [9556858120010252096, "solana_transaction_error", false, 15058459621352422802], [10570260326288551891, "solana_instruction", false, 1372316627393696122], [11091540729177102731, "solana_pubkey", false, 1204822728850583006], [13452659356252768829, "solana_signature", false, 14823358620040957601], [14591356476411885690, "solana_sdk_ids", false, 6221461119085832789], [15429715045911386410, "solana_sanitize", false, 3642558935210287225], [16605237980896264354, "solana_message", false, 18434718862422132362]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/solana-transaction-a287411610a71ff1/dep-lib-solana_transaction", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}